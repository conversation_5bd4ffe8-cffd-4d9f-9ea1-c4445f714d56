from http import client
from langchain_community.vectorstores.timescalevector import TimescaleVector

from src.utils.index_for_vector import ensure_vector_index_hnsw

import weaviate
from weaviate.classes.init import Auth
from langchain_weaviate.vectorstores import WeaviateVectorStore


import os
from dotenv import load_dotenv
load_dotenv()

class WeaviateStore:
    def __init__(self, embedding_model, weaviate_http_host=os.getenv("WEAVIATE_HTTP_HOST"), weaviate_grpc_host=os.getenv("WEAVIATE_GRPC_HOST"), weaviate_http_port=os.getenv("WEAVIATE_HTTP_PORT"), weaviate_grpc_port=os.getenv("WEAVIATE_GRPC_PORT"), weaviate_api_key=os.getenv("WEAVIATE_API_KEY"), collection_name=os.getenv("VECTOR_STORE_COLLECTION")):
        self.embedding_model = embedding_model
        self.weaviate_http_host = weaviate_http_host
        self.weaviate_grpc_host = weaviate_grpc_host
        self.weaviate_http_port = weaviate_http_port
        self.weaviate_api_key = weaviate_api_key
        self.weaviate_grpc_port = weaviate_grpc_port
        self.collection_name = collection_name
        try:
            self.client = weaviate.connect_to_custom(
                http_host=self.weaviate_http_host,
                http_port=self.weaviate_http_port,
                http_secure=False,
                grpc_host=self.weaviate_grpc_host,
                grpc_port=self.weaviate_grpc_port,
                grpc_secure=False,
                auth_credentials=Auth.api_key(self.weaviate_api_key)
            )

            if self.client.collections.exists(self.collection_name):
                self.client.collections.delete(self.collection_name)
                print(f"[Warning] Collection '{self.collection_name}' already exists and has been deleted.")

            self.vector_store = WeaviateVectorStore(
                embedding=self.embedding_model,
                client=self.client,
                text_key="content",
                index_name=self.collection_name,
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
        else:
            print("[Warning] Vector store not available; documents not added.")

    def close_client(self):
        if self.client:
            self.client.close()
        else:
            print("[Warning] No client to close.")

class TimescaleDB:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name):
        self.embedding_model = embedding_model
        self.service_url = service_url
        self.num_dimensions = num_dimensions
        self.table_name = table_name
        try:
            self.vector_store = TimescaleVector(
                embedding=self.embedding_model,
                service_url=self.service_url,
                collection_name=self.table_name,
                num_dimensions=self.num_dimensions
            )
            print(f"[Info] Initialized TimescaleDB vector store with collection '{self.table_name}'.")
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents, create_index=True):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
            if create_index:
                ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; documents not added.")
    
    def create_index_to_vectors(self):
        if self.vector_store:
            ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; index not created.")

class VectorStoreManager:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name=os.getenv("VECTOR_STORE_COLLECTION")):
        self.vector_store = WeaviateStore(embedding_model, service_url, num_dimensions, table_name)

    def add_documents(self, langchain_documents):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents=langchain_documents)
        else:
            print("[Warning] Vector store not available; documents not added.")