import weaviate
from weaviate.classes.init import Auth
from langchain_weaviate.vectorstores import WeaviateVectorStore

import os
from dotenv import load_dotenv
load_dotenv()

class WeaviateStore:
    def __init__(self, embedding_model, weaviate_http_host=os.getenv("WEAVIATE_HTTP_HOST"), weaviate_grpc_host=os.getenv("WEAVIATE_GRPC_HOST"), weaviate_http_port=os.getenv("WEAVIATE_HTTP_PORT"), weaviate_grpc_port=os.getenv("WEAVIATE_GRPC_PORT"), weaviate_api_key=os.getenv("WEAVIATE_API_KEY"), collection_name=os.getenv("VECTOR_STORE_COLLECTION")):
        self.embedding_model = embedding_model
        self.weaviate_http_host = weaviate_http_host
        self.weaviate_grpc_host = weaviate_grpc_host
        self.weaviate_http_port = weaviate_http_port
        self.weaviate_api_key = weaviate_api_key
        self.weaviate_grpc_port = weaviate_grpc_port
        self.collection_name = collection_name
        try:
            self.client = weaviate.connect_to_custom(
                http_host=self.weaviate_http_host,
                http_port=self.weaviate_http_port,
                http_secure=False,
                grpc_host=self.weaviate_grpc_host,
                grpc_port=self.weaviate_grpc_port,
                grpc_secure=False,
                auth_credentials=Auth.api_key(self.weaviate_api_key)
            )

            self.vector_store = WeaviateVectorStore(
                embedding=self.embedding_model,
                client=self.client,
                text_key="content",
                index_name=self.collection_name,
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None