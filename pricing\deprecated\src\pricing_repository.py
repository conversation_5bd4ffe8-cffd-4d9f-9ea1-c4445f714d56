# db_repository.py
import psycopg2
from typing import List, Dict, Any, Optional, Tuple

class ProductRepository:
    def __init__(self, db_config):
        self.db_config = db_config
        self.conn = psycopg2.connect(**db_config)
    
    def get_product_id(self, product_name: str) -> Optional[int]:
        """Get product ID by name"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT id FROM public.products WHERE name ILIKE %s
            """, (product_name,))
            result = cur.fetchone()
            return result[0] if result else None
    
    def get_pricing(self, product_id: int, country: str, subscription: str) -> Optional[Tuple[float, str]]:
        """Get pricing for a product, country and subscription type"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT price_per_month, currency FROM public.pricing
                WHERE product_id = %s AND country = %s AND subscription = %s
            """, (product_id, country, subscription))
            result = cur.fetchone()
            return result if result else None
    
    def get_country_pricing(self, country: str) -> List[Dict[str, Any]]:
        """Get all pricing options for a specific country"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT p.name as product_name, pr.subscription, pr.price_per_month, pr.currency 
                FROM public.pricing pr
                JOIN public.products p ON pr.product_id = p.id
                WHERE pr.country = %s
            """, (country,))
            return [
                {
                    "product_name": row[0],
                    "subscription": row[1],
                    "price_per_month": float(row[2]),
                    "currency": row[3]
                }
                for row in cur.fetchall()
            ]
    
    def get_all_countries(self) -> List[str]:
        """Get all available countries"""
        with self.conn.cursor() as cur:
            cur.execute("SELECT DISTINCT country FROM public.pricing")
            return [row[0] for row in cur.fetchall()]
        
    def get_all_awailable_products(self, country) -> List[Dict[str, Any]]:
        """Get all products"""
        with self.conn.cursor() as cur:
            cur.execute(
                """SELECT products.id, products.name FROM public.products
                join public.pricing p on products.id = p.product_id
                where p.country = %s""",
                (country,)
            )
            return [
                {
                    "id": row[0],
                    "name": row[1]
                }
                for row in cur.fetchall()
            ]
    
    def get_all_products(self) -> List[Dict[str, Any]]:
        """Get all products"""
        with self.conn.cursor() as cur:
            cur.execute("SELECT id, name FROM public.products")
            return [
                {
                    "id": row[0],
                    "name": row[1]
                }
                for row in cur.fetchall()
            ]
    
    def close(self):
        """Close database connection"""
        self.conn.close()