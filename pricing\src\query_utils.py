from typing import List, Dict, Any
from company_registry import company_registry


class PricingQueryUtils:
    """Utility functions for querying pricing data across all supported companies."""
    
    def __init__(self):
        """Initialize the query utilities."""
        self.registry = company_registry
    
    def get_countries(self, company_name: str) -> List[str]:
        """
        Get list of available countries for any supported company.

        Args:
            company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

        Returns:
            list: List of country codes
        """
        if not self.registry.is_supported_company(company_name):
            raise ValueError(f"Company '{company_name}' not found.")
        
        pricing_data = self.registry.get_company_data(company_name)
        return list(pricing_data.keys()) if pricing_data else []
    
    def get_term_options(self, company_name: str) -> List[str]:
        """
        Get available term options for any supported company.

        Args:
            company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

        Returns:
            list: List of available term IDs
        """
        if not self.registry.is_supported_company(company_name):
            raise ValueError(f"Company '{company_name}' not found.")
        
        pricing_data = self.registry.get_company_data(company_name)
        return self._get_standardized_term_options(pricing_data) if pricing_data else []
    
    def _get_standardized_term_options(self, pricing_data: dict) -> List[str]:
        """Helper function to get term options from standardized pricing data."""
        terms = set()
        for country_data in pricing_data.values():
            for subscription_data in country_data.get("subscriptions", {}).values():
                for term_info in subscription_data.get("terms", []):
                    terms.add(term_info.get("term_id"))
        return list(terms)
    
    def get_country_pricing(self, company_name: str, country_code: str) -> Dict[str, Any]:
        """
        Get pricing data for a specific country for any supported company.

        Args:
            company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
            country_code: Country code (e.g., "US")

        Returns:
            dict: Country pricing data
        """
        if not self.registry.is_supported_company(company_name):
            raise ValueError(f"Company '{company_name}' not found.")
        
        pricing_data = self.registry.get_company_data(company_name)
        
        if not pricing_data:
            raise ValueError(f"{company_name} pricing data not available")

        if country_code not in pricing_data:
            raise ValueError(f"Country '{country_code}' not found in {company_name} pricing data")

        return pricing_data[country_code]
    
    def get_subscriptions(self, company_name: str, country_code: str) -> List[str]:
        """
        Get available subscriptions for a specific country for any supported company.

        Args:
            company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
            country_code: Country code (e.g., "US")

        Returns:
            list: List of available subscription names
        """
        if not self.registry.is_supported_company(company_name):
            raise ValueError(f"Company '{company_name}' not found.")
        
        pricing_data = self.registry.get_company_data(company_name)
        
        if not pricing_data:
            raise ValueError(f"{company_name} pricing data not available")

        if country_code not in pricing_data:
            raise ValueError(f"Country '{country_code}' not found in {company_name} pricing data")

        country_data = pricing_data[country_code]
        subscriptions = list(country_data.get("subscriptions", {}).keys())
        return subscriptions
    
    def get_all_supported_companies(self) -> List[str]:
        """
        Get list of all supported companies.
        
        Returns:
            list: List of supported company names
        """
        return self.registry.get_supported_companies()


# Global instance for use throughout the application
query_utils = PricingQueryUtils()
