from typing import List, Tuple
from langchain_core.documents import Document
from langchain_community.vectorstores.timescalevector import TimescaleVector

from weaviate.classes.query import Filter

from embedding import EmbeddingModelForSearch
from vector_store import WeaviateStore

import os
from dotenv import load_dotenv

load_dotenv()



class SearchService:
    def __init__(self):
        # Load environment variables
        self.service_url = ""
        self.collection_name = os.environ.get("VECTOR_STORE_COLLECTION", "rag_chunks")
        self.embedding_model = EmbeddingModelForSearch().embedding_model
        # Initialize TimescaleVector
        self.vs = WeaviateStore(
            embedding_model=self.embedding_model,
            collection_name=self.collection_name
        )
    
    def search_query(self, query: str, k: int = 20):
        """Run similarity search for multiple queries and return deduplicated top results."""

        result = self.vs.vector_store.similarity_search(query=query, k=k)

        return result
    
    def search_chunks_by_source(self, source: str):
        search_filter = Filter.by_property("source").equal(source)

        filter_result = self.vs.vector_store.similarity_search(query="", filters=search_filter, k=4000)

        final_result = []
        for r in filter_result:
            if(r.metadata.get("source") == source):
                final_result.append(r)

        return final_result

    def client_close(self):
        self.vs.client.close()

