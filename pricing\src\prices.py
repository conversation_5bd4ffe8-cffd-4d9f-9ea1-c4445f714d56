import json
import os


# Get the directory containing this file
PRICING_DIR = os.path.dirname(os.path.abspath(__file__))

# Load Allplan pricing data
allplan_file_path = os.path.join(PRICING_DIR, "allplan_pricing_data.json")
try:
    with open(allplan_file_path, "r", encoding="utf-8") as f:
        allplan_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {allplan_file_path} not found. Allplan pricing functions will not work.")
    allplan_pricing_data = {}

# Load Bluebeam pricing data
bluebeam_file_path = os.path.join(PRICING_DIR, "bluebeam_pricing_data.json")
try:
    with open(bluebeam_file_path, "r", encoding="utf-8") as f:
        bluebeam_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {bluebeam_file_path} not found. Bluebeam pricing functions will not work.")
    bluebeam_pricing_data = {}

# Load Graphisoft pricing data
graphisoft_file_path = os.path.join(PRICING_DIR, "graphisoft_pricing_data.json")
try:
    with open(graphisoft_file_path, "r", encoding="utf-8") as f:
        graphisoft_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {graphisoft_file_path} not found. Graphisoft pricing functions will not work.")
    graphisoft_pricing_data = {}

# Load Vectorworks pricing data
vectorworks_file_path = os.path.join(PRICING_DIR, "vectorworks_pricing_data.json")
try:
    with open(vectorworks_file_path, "r", encoding="utf-8") as f:
        vectorworks_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {vectorworks_file_path} not found. Vectorworks pricing functions will not work.")
    vectorworks_pricing_data = {}


def _calculate_standardized_price(pricing_data: dict, product_name: str, country_code: str, term: str, seats: int, company_name: str):
    """
    Helper function to calculate price using the standardized JSON structure.

    Args:
        pricing_data: The pricing data dictionary for the company
        product_name: Name of the subscription
        country_code: Country code (e.g., "US")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats
        company_name: Name of the company (for error messages)

    Returns:
        dict: Contains price information including total cost
    """
    if not pricing_data:
        raise ValueError(f"{company_name} pricing data not available. Please ensure {company_name.lower()}_pricing_data.json exists.")

    if country_code not in pricing_data:
        raise ValueError(f"Country '{country_code}' not found in {company_name} pricing data")

    country_data = pricing_data[country_code]

    # Find the subscription in the standardized structure
    if product_name not in country_data.get("subscriptions", {}):
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    subscription_data = country_data["subscriptions"][product_name]

    # Find the term in the terms array
    term_data = None
    for term_info in subscription_data.get("terms", []):
        if term_info.get("term_id") == term:
            term_data = term_info
            break

    if not term_data:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    # Calculate pricing based on the standardized structure
    price_per_month = term_data.get("price_per_month", 0)
    total_price_per_seat = term_data.get("total_price", price_per_month)

    if total_price_per_seat is None:
        raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

    total_price = total_price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "country_name": country_data.get("country_name"),
        "term": term,
        "seats": seats,
        "price_per_month": price_per_month,
        "price_per_seat_total": total_price_per_seat,
        "total_price": total_price,
        "currency": term_data.get("currency"),
        "commitment": term_data.get("commitment"),
        "billing": term_data.get("billing")
    }


def calculate_price(product_name: str, country_code: str, term: str, seats: int, company_name: str):
    """
    Calculate the total price for any supported company's subscription.

    Args:
        product_name: Name of the subscription
        country_code: Country code (e.g., "US")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

    Returns:
        dict: Contains price information including total cost
    """
    company_lower = company_name.lower()

    if company_lower == "allplan":
        return _calculate_standardized_price(allplan_pricing_data, product_name, country_code, term, seats, "Allplan")
    elif company_lower == "bluebeam":
        return _calculate_standardized_price(bluebeam_pricing_data, product_name, country_code, term, seats, "Bluebeam")
    elif company_lower == "graphisoft":
        return _calculate_standardized_price(graphisoft_pricing_data, product_name, country_code, term, seats, "Graphisoft")
    elif company_lower == "vectorworks":
        return _calculate_standardized_price(vectorworks_pricing_data, product_name, country_code, term, seats, "Vectorworks")
    else:
        raise ValueError(f"Company '{company_name}' not found.")

def get_countries(company_name: str):
    """
    Get list of available countries for any supported company.

    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

    Returns:
        list: List of country codes
    """
    company_lower = company_name.lower()

    if company_lower == "allplan":
        return list(allplan_pricing_data.keys())
    elif company_lower == "bluebeam":
        return list(bluebeam_pricing_data.keys())
    elif company_lower == "graphisoft":
        return list(graphisoft_pricing_data.keys()) if graphisoft_pricing_data else []
    elif company_lower == "vectorworks":
        return list(vectorworks_pricing_data.keys())
    else:
        raise ValueError(f"Company '{company_name}' not found.")

def get_term_options(company_name: str):
    """
    Get available term options for any supported company.

    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")

    Returns:
        list: List of available term IDs
    """
    company_lower = company_name.lower()

    if company_lower == "allplan":
        return _get_standardized_term_options(allplan_pricing_data)
    elif company_lower == "bluebeam":
        return _get_standardized_term_options(bluebeam_pricing_data)
    elif company_lower == "graphisoft":
        return _get_standardized_term_options(graphisoft_pricing_data) if graphisoft_pricing_data else []
    elif company_lower == "vectorworks":
        return _get_standardized_term_options(vectorworks_pricing_data)
    else:
        raise ValueError(f"Company '{company_name}' not found.")

def _get_standardized_term_options(pricing_data: dict):
    """Helper function to get term options from standardized pricing data."""
    terms = set()
    for country_data in pricing_data.values():
        for subscription_data in country_data.get("subscriptions", {}).values():
            for term_info in subscription_data.get("terms", []):
                terms.add(term_info.get("term_id"))
    return list(terms)

def get_country_pricing(company_name: str, country_code: str):
    """
    Get pricing data for a specific country for any supported company.

    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
        country_code: Country code (e.g., "US")

    Returns:
        dict: Country pricing data
    """
    company_lower = company_name.lower()

    if company_lower == "allplan":
        pricing_data = allplan_pricing_data
    elif company_lower == "bluebeam":
        pricing_data = bluebeam_pricing_data
    elif company_lower == "graphisoft":
        pricing_data = graphisoft_pricing_data
    elif company_lower == "vectorworks":
        pricing_data = vectorworks_pricing_data
    else:
        raise ValueError(f"Company '{company_name}' not found.")

    if not pricing_data:
        raise ValueError(f"{company_name} pricing data not available")

    if country_code not in pricing_data:
        raise ValueError(f"Country '{country_code}' not found in {company_name} pricing data")

    return pricing_data[country_code]

def get_subscriptions(company_name: str, country_code: str):
    """
    Get available subscriptions for a specific country for any supported company.

    Args:
        company_name: Company name ("allplan", "bluebeam", "graphisoft", "vectorworks")
        country_code: Country code (e.g., "US")

    Returns:
        list: List of available subscription names
    """
    company_lower = company_name.lower()

    if company_lower == "allplan":
        pricing_data = allplan_pricing_data
    elif company_lower == "bluebeam":
        pricing_data = bluebeam_pricing_data
    elif company_lower == "graphisoft":
        pricing_data = graphisoft_pricing_data
    elif company_lower == "vectorworks":
        pricing_data = vectorworks_pricing_data
    else:
        raise ValueError(f"Company '{company_name}' not found.")

    if not pricing_data:
        raise ValueError(f"{company_name} pricing data not available")

    if country_code not in pricing_data:
        raise ValueError(f"Country '{country_code}' not found in {company_name} pricing data")

    country_data = pricing_data[country_code]
    subscriptions = list(country_data.get("subscriptions", {}).keys())
    return subscriptions

