import json
import os

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(BASE_DIR, "inverse_providers_data.json")

providers_by_country = []

with open(file_path, "r", encoding="utf-8") as inverse_providers_data:
    providers_by_country = json.loads(inverse_providers_data.read())

def is_valid_provider(provider, products: list[str]):
    for product in products:
        if product not in provider["products"]:
            return False
    return True


def get_providers(country_code: str, products: list[str] = []):
    providers = []
    possible_providers = providers_by_country[country_code]
    for possible_provider in possible_providers:
        if (is_valid_provider(possible_provider, products)):
            providers.append(possible_provider)
    return providers

