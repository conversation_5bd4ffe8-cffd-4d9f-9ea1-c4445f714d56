from src.utils.doc_with_source import DocWithSource
from src.utils.md_mongo_loader import MDMongoLoader
from src.utils.chunking import ChunkingUtils
from src.utils.save_to_vector_store import DocsSaver
from src.utils.ts_service_url import get_vector_store_service_url
from src.utils.deprecate_db_table import ts_deprecate_db_table
from tqdm import tqdm

from weaviate.classes.query import Filter


from src.database.vector_store import WeaviateStore

from src.models.embedding import EmbeddingModelForVectorStore

from src.utils.docs_convert_langchainDocument import ConvertDocsToLangchainDocumentsWithoutContext


def main():
    try:
        embedding_model = EmbeddingModelForVectorStore()

        vector_store = WeaviateStore(
            embedding_model=embedding_model.embedding_model,
        )
        
        mongo_loader = MDMongoLoader()
        docs=mongo_loader.loadAllMarkDown_fromMongoDB()

        print(f"Loaded {len(docs)} documents from MongoDB.")

        chunking_utils = ChunkingUtils(chunk_size=2000, chunk_overlap=400)
        chunking_utils.addChunkListsTo(docs)

        print(f"Chunked documents into {sum(len(doc.chunkList) for doc in docs)} chunks.")

        docs_saver = DocsSaver(            
            documents=docs,
            vector_store=vector_store,
            batch_size=100
        )
        docs_saver.saveChunksToVectoreStoreWithoutContext()

    finally:
        vector_store.close_client()



if __name__ == "__main__":
    main()
